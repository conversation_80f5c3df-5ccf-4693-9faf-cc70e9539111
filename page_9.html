<!DOCTYPE html><html lang="zh"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>联系我们</title>

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        body {width: 1280px; min-height: 720px; margin: 0;
            padding: 0;}
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #0f2a4a 0%, #1a4a7c 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .content-box {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .contact-card {
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
        }
        .accent-text {
            color: #ff7b00;
        }
        .contact-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: rgba(255, 123, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }
        .cta-button {
            background: linear-gradient(90deg, #ff7b00, #ff9e00);
            transition: all 0.3s ease;
        }
        .cta-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 123, 0, 0.4);
        }
        .particle {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            animation: float 8s infinite ease-in-out;
        }
        @keyframes float {
            0%, 100% {
                transform: translateY(0) translateX(0);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) translateX(15px);
                opacity: 0.7;
            }
        }
    </style>
</head>
<body>
<div class="slide p-12 flex flex-col items-center justify-center relative">
<!-- Decorative particles -->
<div class="particle" style="width: 10px; height: 10px; top: 10%; left: 20%; animation-delay: 0s;"></div>
<div class="particle" style="width: 15px; height: 15px; top: 30%; left: 80%; animation-delay: 1s;"></div>
<div class="particle" style="width: 8px; height: 8px; top: 70%; left: 15%; animation-delay: 2s;"></div>
<div class="particle" style="width: 12px; height: 12px; top: 60%; left: 75%; animation-delay: 3s;"></div>
<div class="particle" style="width: 20px; height: 20px; top: 40%; left: 30%; animation-delay: 4s;"></div>
<!-- Title -->
<h1 class="title text-center mb-6">联系我们</h1>
<!-- Tagline -->
<p class="text-xl text-center mb-10 max-w-3xl">
<span class="accent-text font-bold">立即体验</span>AI短剧创作的无限可能，开启内容创作新纪元
        </p>
<!-- Main Content -->
<div class="content-box p-8 w-full max-w-4xl flex flex-col items-center">
<!-- Contact Information -->
<div class="grid grid-cols-3 gap-8 w-full mb-10">
<!-- Contact Card 1 -->
<div class="contact-card p-6 flex flex-col items-center text-center">
<div class="contact-icon">
<i class="fas fa-envelope text-3xl text-orange-400"></i>
</div>
<h3 class="text-lg font-semibold mb-2">电子邮箱</h3>
<p class="text-gray-300"><EMAIL></p>
</div>
<!-- Contact Card 2 -->
<div class="contact-card p-6 flex flex-col items-center text-center">
<div class="contact-icon">
<i class="fas fa-globe text-3xl text-orange-400"></i>
</div>
<h3 class="text-lg font-semibold mb-2">官方网站</h3>
<p class="text-gray-300">www.ai-shortdrama.com</p>
</div>
<!-- Contact Card 3 -->
<div class="contact-card p-6 flex flex-col items-center text-center">
<div class="contact-icon">
<i class="fas fa-phone text-3xl text-orange-400"></i>
</div>
<h3 class="text-lg font-semibold mb-2">联系电话</h3>
<p class="text-gray-300">400-888-XXXX</p>
</div>
</div>
<!-- Social Media -->
<div class="flex justify-center space-x-8 mb-10">
<div class="flex items-center">
<i class="fab fa-weixin text-3xl text-green-500 mr-2"></i>
<span class="text-lg">微信公众号</span>
</div>
<div class="flex items-center">
<i class="fab fa-weibo text-3xl text-red-500 mr-2"></i>
<span class="text-lg">微博</span>
</div>
<div class="flex items-center">
<i class="fab fa-youtube text-3xl text-red-600 mr-2"></i>
<span class="text-lg">YouTube</span>
</div>
<div class="flex items-center">
<i class="fab fa-tiktok text-3xl text-gray-200 mr-2"></i>
<span class="text-lg">TikTok</span>
</div>
</div>
<!-- Call to Action -->
<button class="cta-button px-10 py-3 rounded-full text-xl font-bold text-white flex items-center">
<i class="fas fa-rocket mr-2"></i>
                立即体验AI短剧创作
            </button>
</div>
<!-- Footer -->
<div class="mt-8 text-center text-sm text-gray-400">
<p>© 2025 AI短剧生成应用 | 报告生成时间: 2025-08-04</p>
</div>
</div>

</body></html>