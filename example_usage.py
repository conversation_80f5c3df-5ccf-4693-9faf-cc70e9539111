#!/usr/bin/env python3
"""
使用示例：演示如何使用网页转PPTX工具
"""

import os
from webpage_to_pptx import WebPageToPPTX
from batch_html_to_pptx import BatchHTMLToPPTX


def example_single_file():
    """示例：转换单个HTML文件"""
    print("=== 示例1：转换单个HTML文件 ===")
    
    # 检查是否存在HTML文件
    html_files = [f for f in os.listdir('.') if f.endswith('.html')]
    if not html_files:
        print("当前目录没有HTML文件")
        return
    
    # 选择第一个HTML文件进行转换
    html_file = html_files[0]
    output_file = f"{os.path.splitext(html_file)[0]}_example.pptx"
    
    try:
        converter = WebPageToPPTX()
        converter.convert_file_to_pptx(html_file, output_file)
        print(f"成功转换 {html_file} -> {output_file}")
    except Exception as e:
        print(f"转换失败: {e}")


def example_batch_processing():
    """示例：批量处理HTML文件"""
    print("\n=== 示例2：批量处理HTML文件 ===")
    
    try:
        batch_processor = BatchHTMLToPPTX()
        
        # 创建输出目录
        output_dir = "example_output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 批量转换
        batch_processor.process_all_html_files(output_dir=output_dir)
        print(f"批量转换完成，输出目录: {output_dir}")
        
    except Exception as e:
        print(f"批量处理失败: {e}")


def example_merge_files():
    """示例：合并多个HTML文件为一个PPTX"""
    print("\n=== 示例3：合并HTML文件 ===")
    
    try:
        batch_processor = BatchHTMLToPPTX()
        output_file = "merged_example.pptx"
        
        batch_processor.merge_html_files_to_single_pptx(output_file=output_file)
        print(f"合并完成: {output_file}")
        
    except Exception as e:
        print(f"合并失败: {e}")


def example_online_webpage():
    """示例：转换在线网页"""
    print("\n=== 示例4：转换在线网页 ===")
    
    # 使用一个简单的示例网页
    test_urls = [
        "https://httpbin.org/html",  # 简单的HTML测试页面
        "https://example.com"        # 经典示例网站
    ]
    
    converter = WebPageToPPTX()
    
    for i, url in enumerate(test_urls, 1):
        try:
            output_file = f"online_example_{i}.pptx"
            print(f"正在转换: {url}")
            converter = WebPageToPPTX()  # 为每个URL创建新实例
            converter.convert_url_to_pptx(url, output_file)
            print(f"成功转换: {output_file}")
            break  # 成功一个就退出
        except Exception as e:
            print(f"转换 {url} 失败: {e}")
            continue


def main():
    """运行所有示例"""
    print("网页转PPTX工具使用示例")
    print("=" * 40)
    
    # 检查依赖
    try:
        import requests
        import bs4
        import pptx
        print("✓ 所有依赖包已安装")
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return
    
    # 运行示例
    example_single_file()
    example_batch_processing()
    example_merge_files()
    
    # 在线示例（可选）
    choice = input("\n是否测试在线网页转换？(y/n): ").lower().strip()
    if choice == 'y':
        example_online_webpage()
    
    print("\n所有示例运行完成！")
    print("生成的文件:")
    for file in os.listdir('.'):
        if file.endswith('.pptx'):
            print(f"  - {file}")


if __name__ == "__main__":
    main()
