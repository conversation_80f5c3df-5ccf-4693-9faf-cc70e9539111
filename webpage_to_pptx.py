#!/usr/bin/env python3
"""
网页转PPTX工具
支持将网页内容转换为PowerPoint演示文稿
"""

import os
import requests
from bs4 import BeautifulSoup
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
import re
from urllib.parse import urljoin, urlparse
import argparse
from typing import List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WebPageToPPTX:
    """网页转PPTX转换器"""
    
    def __init__(self):
        self.prs = Presentation()
        self.slide_width = self.prs.slide_width
        self.slide_height = self.prs.slide_height
        
    def fetch_webpage(self, url: str) -> BeautifulSoup:
        """获取网页内容"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            response.encoding = response.apparent_encoding
            
            soup = BeautifulSoup(response.text, 'html.parser')
            logger.info(f"成功获取网页: {url}")
            return soup
            
        except Exception as e:
            logger.error(f"获取网页失败 {url}: {str(e)}")
            raise
    
    def read_local_html(self, file_path: str) -> BeautifulSoup:
        """读取本地HTML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            soup = BeautifulSoup(content, 'html.parser')
            logger.info(f"成功读取本地文件: {file_path}")
            return soup
        except Exception as e:
            logger.error(f"读取本地文件失败 {file_path}: {str(e)}")
            raise
    
    def extract_content(self, soup: BeautifulSoup) -> Dict:
        """提取网页主要内容"""
        content = {
            'title': '',
            'headings': [],
            'paragraphs': [],
            'lists': [],
            'images': []
        }
        
        # 提取标题
        title_tag = soup.find('title')
        if title_tag:
            content['title'] = title_tag.get_text().strip()
        
        # 提取各级标题
        for i in range(1, 7):
            headings = soup.find_all(f'h{i}')
            for heading in headings:
                text = heading.get_text().strip()
                if text:
                    content['headings'].append({
                        'level': i,
                        'text': text
                    })
        
        # 提取段落
        paragraphs = soup.find_all('p')
        for p in paragraphs:
            text = p.get_text().strip()
            if text and len(text) > 10:  # 过滤太短的段落
                content['paragraphs'].append(text)
        
        # 提取列表
        lists = soup.find_all(['ul', 'ol'])
        for lst in lists:
            items = []
            for li in lst.find_all('li'):
                text = li.get_text().strip()
                if text:
                    items.append(text)
            if items:
                content['lists'].append({
                    'type': lst.name,
                    'items': items
                })
        
        return content
    
    def create_title_slide(self, title: str):
        """创建标题幻灯片"""
        slide_layout = self.prs.slide_layouts[0]  # 标题布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        title_shape = slide.shapes.title
        title_shape.text = title or "网页内容演示"
        
        # 设置标题样式
        title_paragraph = title_shape.text_frame.paragraphs[0]
        title_paragraph.alignment = PP_ALIGN.CENTER
        title_paragraph.font.size = Pt(44)
        title_paragraph.font.bold = True
        title_paragraph.font.color.rgb = RGBColor(0, 51, 102)
        
        logger.info(f"创建标题幻灯片: {title}")
    
    def create_content_slide(self, title: str, content: List[str]):
        """创建内容幻灯片"""
        slide_layout = self.prs.slide_layouts[1]  # 标题和内容布局
        slide = self.prs.slides.add_slide(slide_layout)
        
        # 设置标题
        title_shape = slide.shapes.title
        title_shape.text = title
        
        # 设置内容
        content_shape = slide.shapes.placeholders[1]
        text_frame = content_shape.text_frame
        text_frame.clear()
        
        for i, item in enumerate(content):
            if i == 0:
                p = text_frame.paragraphs[0]
            else:
                p = text_frame.add_paragraph()
            
            p.text = item
            p.level = 0
            p.font.size = Pt(18)
            
        logger.info(f"创建内容幻灯片: {title}")
    
    def create_list_slide(self, title: str, list_data: Dict):
        """创建列表幻灯片"""
        slide_layout = self.prs.slide_layouts[1]
        slide = self.prs.slides.add_slide(slide_layout)
        
        title_shape = slide.shapes.title
        title_shape.text = title
        
        content_shape = slide.shapes.placeholders[1]
        text_frame = content_shape.text_frame
        text_frame.clear()
        
        for i, item in enumerate(list_data['items']):
            if i == 0:
                p = text_frame.paragraphs[0]
            else:
                p = text_frame.add_paragraph()
            
            p.text = f"• {item}"
            p.level = 0
            p.font.size = Pt(16)
            
        logger.info(f"创建列表幻灯片: {title}")
    
    def process_content(self, content: Dict):
        """处理内容并创建幻灯片"""
        # 创建标题幻灯片
        self.create_title_slide(content['title'])
        
        # 按标题分组内容
        current_section = "概述"
        section_content = []
        
        # 处理段落内容
        for paragraph in content['paragraphs']:
            if len(paragraph) > 200:  # 长段落分割
                chunks = [paragraph[i:i+200] for i in range(0, len(paragraph), 200)]
                section_content.extend(chunks)
            else:
                section_content.append(paragraph)
            
            # 每3-4个段落创建一张幻灯片
            if len(section_content) >= 3:
                self.create_content_slide(current_section, section_content[:3])
                section_content = section_content[3:]
        
        # 处理剩余内容
        if section_content:
            self.create_content_slide(current_section, section_content)
        
        # 处理标题
        for heading in content['headings']:
            if heading['level'] <= 2:  # 只处理主要标题
                self.create_content_slide(heading['text'], [])
        
        # 处理列表
        for i, lst in enumerate(content['lists']):
            title = f"要点 {i+1}" if len(content['lists']) > 1 else "要点"
            self.create_list_slide(title, lst)
    
    def convert_url_to_pptx(self, url: str, output_file: str):
        """将网页URL转换为PPTX"""
        soup = self.fetch_webpage(url)
        content = self.extract_content(soup)
        self.process_content(content)
        self.save_presentation(output_file)
    
    def convert_file_to_pptx(self, html_file: str, output_file: str):
        """将本地HTML文件转换为PPTX"""
        soup = self.read_local_html(html_file)
        content = self.extract_content(soup)
        self.process_content(content)
        self.save_presentation(output_file)
    
    def save_presentation(self, output_file: str):
        """保存演示文稿"""
        try:
            self.prs.save(output_file)
            logger.info(f"演示文稿已保存: {output_file}")
        except Exception as e:
            logger.error(f"保存演示文稿失败: {str(e)}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将网页转换为PPTX演示文稿')
    parser.add_argument('--url', help='网页URL')
    parser.add_argument('--file', help='本地HTML文件路径')
    parser.add_argument('--output', '-o', default='output.pptx', help='输出PPTX文件名')
    
    args = parser.parse_args()
    
    if not args.url and not args.file:
        print("请提供网页URL (--url) 或本地HTML文件路径 (--file)")
        return
    
    converter = WebPageToPPTX()
    
    try:
        if args.url:
            converter.convert_url_to_pptx(args.url, args.output)
        else:
            converter.convert_file_to_pptx(args.file, args.output)
        
        print(f"转换完成！输出文件: {args.output}")
        
    except Exception as e:
        print(f"转换失败: {str(e)}")


if __name__ == "__main__":
    main()
