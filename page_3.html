<!DOCTYPE html><html lang="zh"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>智能剧本创作</title>

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        body {width: 1280px; min-height: 802px; margin: 0;
            padding: 0;}
        .slide {
            width: 1280px;
            min-height: 802px;
            position: relative;
            background: linear-gradient(135deg, #1a365d 0%, #2a4365 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .title {
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .feature-box {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }
        .feature-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        .feature-icon {
            color: #ed8936;
            font-size: 1.8rem;
        }
        .accent-line {
            background: linear-gradient(90deg, #ed8936 0%, #f6ad55 100%);
            height: 4px;
            width: 100px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
<div class="slide p-12 flex flex-col">
<!-- Header -->
<div class="mb-8">
<h1 class="title">智能剧本创作</h1>
<div class="accent-line"></div>
<p class="text-xl mt-4 text-gray-200">AI驱动的创意写作新时代</p>
</div>
<!-- Main Content -->
<div class="flex flex-1">
<!-- Left Column: Features -->
<div class="w-1/2 pr-8 flex flex-col justify-between">
<!-- Feature 1 -->
<div class="feature-box p-4 mb-4">
<div class="flex items-start">
<i class="fas fa-magic feature-icon mr-4 mt-1"></i>
<div>
<h3 class="text-xl font-semibold mb-2">一键启动创意</h3>
<p class="text-gray-300">只需输入主题、风格或梗概，AI大语言模型迅速构建完整故事世界，将创意转化为结构化剧本</p>
</div>
</div>
</div>
<!-- Feature 2 -->
<div class="feature-box p-4 mb-4">
<div class="flex items-start">
<i class="fas fa-lightbulb feature-icon mr-4 mt-1"></i>
<div>
<h3 class="text-xl font-semibold mb-2">多样化与创新性</h3>
<p class="text-gray-300">突破传统思维定式，提供多种故事走向和创意方案，探索全新的叙事可能性</p>
</div>
</div>
</div>
<!-- Feature 3 -->
<div class="feature-box p-4 mb-4">
<div class="flex items-start">
<i class="fas fa-edit feature-icon mr-4 mt-1"></i>
<div>
<h3 class="text-xl font-semibold mb-2">可视化编辑</h3>
<p class="text-gray-300">像使用文档编辑器一样，轻松修改剧本的任何部分，包括对话、情节节奏和人物性格</p>
</div>
</div>
</div>
<!-- Feature 4 -->
<div class="feature-box p-4">
<div class="flex items-start">
<i class="fas fa-sync-alt feature-icon mr-4 mt-1"></i>
<div>
<h3 class="text-xl font-semibold mb-2">创意迭代与优化</h3>
<p class="text-gray-300">向AI提出进一步修改指令，如&#34;让这段对话更紧张&#34;，AI将根据要求进行迭代优化</p>
</div>
</div>
</div>
</div>
<!-- Right Column: Image and Collaborative Model -->
<div class="w-1/2 pl-8 flex flex-col">
<!-- Image -->
<div class="mb-6 rounded-lg overflow-hidden">
<img alt="AI剧本生成界面" class="w-full h-auto object-cover shadow-lg" src="https://picture-search.skywork.ai/aippt/image/sheet/6d4f56ac866591ee50412ad3939dddfe.jpg" style="max-height: 350px;" data-sk-source-container="img" data-sk-source-url="https://www.cnblogs.com/treasury/articles/18749039" data-sk-source-id="1952183924410892288"/>
</div>
<!-- Collaborative Model Box -->
<div class="feature-box p-6 mt-auto">
<div class="flex items-center mb-4">
<i class="fas fa-robot feature-icon mr-4"></i>
<i class="fas fa-user-edit feature-icon mr-4"></i>
<h3 class="text-xl font-semibold">&#34;AI赋能，人类主导&#34;的协同模式</h3>
</div>
<p class="text-gray-300 mb-4">AI负责构建框架与提升效率，您专注于为故事注入情感共鸣与人文深度</p>
<div class="flex justify-between items-center">
<div class="text-center">
<i class="fas fa-robot text-2xl text-gray-400"></i>
<p class="text-sm mt-2 text-gray-400">AI效率</p>
</div>
<div class="w-24 h-1 bg-gray-600 rounded-full relative">
<div class="absolute w-6 h-6 rounded-full bg-orange-500 top-1/2 transform -translate-y-1/2 right-0"></div>
</div>
<div class="text-center">
<i class="fas fa-user-edit text-2xl text-orange-500"></i>
<p class="text-sm mt-2 text-orange-500">人类创造力</p>
</div>
</div>
</div>
</div>
</div>
<!-- Footer -->
<div class="mt-6 text-right text-sm text-gray-400">
<p>第3页 / 共8页</p>
</div>
</div>

</body></html>