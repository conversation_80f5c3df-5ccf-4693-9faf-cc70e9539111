<!DOCTYPE html><html lang="zh"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>市场背景与应用价值</title>

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://d3js.org/d3.v7.min.js"></script>
<script src="https://cdn.tailwindcss.com"></script><style>
        body {width: 1280px; min-height: 818px; margin: 0;
            padding: 0;}
        .slide {
            width: 1280px;
            min-height: 818px;
            position: relative;
            background: linear-gradient(135deg, #0f2a4a 0%, #1a4a7c 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .content-box {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .stat-box {
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        .stat-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
        }
        .accent-text {
            color: #ff7b00;
        }
        .chart-container {
            height: 250px;
        }
    </style>
</head>
<body>
<div class="slide p-12 flex flex-col">
<!-- Title -->
<h1 class="title text-center mb-8">市场背景与应用价值</h1>
<!-- Main Content -->
<div class="flex flex-1">
<!-- Left Column - Market Stats -->
<div class="w-1/2 pr-6">
<div class="content-box p-6 h-full flex flex-col justify-between">
<h2 class="text-2xl font-semibold mb-4">
<i class="fas fa-chart-line mr-2 text-orange-400"></i>微短剧市场现状
                    </h2>
<!-- Market Stats -->
<div class="flex flex-col space-y-6 flex-1">
<div class="stat-box p-4 flex items-center">
<div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4">
<i class="fas fa-coins text-3xl text-white"></i>
</div>
<div>
<p class="text-gray-300">市场规模</p>
<p class="text-3xl font-bold"><span class="accent-text">505</span> 亿元</p>
</div>
</div>
<div class="stat-box p-4 flex items-center">
<div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4">
<i class="fas fa-users text-3xl text-white"></i>
</div>
<div>
<p class="text-gray-300">用户基数</p>
<p class="text-3xl font-bold"><span class="accent-text">5.76</span> 亿人</p>
</div>
</div>
</div>
<!-- Chart -->
<div class="chart-container mt-4" id="chart"></div>
</div>
</div>
<!-- Right Column - Value Proposition -->
<div class="w-1/2 pl-6">
<div class="content-box p-6 h-full">
<h2 class="text-2xl font-semibold mb-4">
<i class="fas fa-lightbulb mr-2 text-orange-400"></i>行业痛点与解决方案
                    </h2>
<!-- Traditional vs AI-Driven -->
<div class="flex flex-col space-y-6">
<div class="bg-red-900 bg-opacity-30 p-4 rounded-lg">
<h3 class="text-xl font-medium mb-2 flex items-center">
<i class="fas fa-exclamation-triangle text-red-400 mr-2"></i>
                                传统制作模式的痛点
                            </h3>
<ul class="list-disc list-inside space-y-2 text-gray-200">
<li>制作周期长，成本高昂</li>
<li>流程繁琐，效率低下</li>
<li>难以满足内容快速迭代需求</li>
<li>创意表达受限，缺乏多样性</li>
</ul>
</div>
<div class="bg-green-900 bg-opacity-30 p-4 rounded-lg">
<h3 class="text-xl font-medium mb-2 flex items-center">
<i class="fas fa-check-circle text-green-400 mr-2"></i>
                                AI短剧生成应用的创新解决方案
                            </h3>
<ul class="list-disc list-inside space-y-2 text-gray-200">
<li>AI赋能+人工主导的协同创作模式</li>
<li>将灵感到成片的完整创作链路集于一体</li>
<li>实现制作效率的指数级提升</li>
<li>大幅压缩创作成本</li>
<li>激发无限创意可能</li>
</ul>
</div>
</div>
</div>
</div>
</div>
<!-- Footer -->
<div class="mt-8 text-center text-sm text-gray-400">
<p>通过革命性的AI短剧生成技术，重构内容生产流程，开启高效、高质的短剧创作之旅</p>
</div>
</div>
<script>
        // Simple D3.js visualization for market growth
        const chartData = [
            {year: "2023", value: 300},
            {year: "2024", value: 400},
            {year: "2025", value: 505}
        ];
        
        const margin = {top: 20, right: 30, bottom: 40, left: 50};
        const width = document.getElementById('chart').offsetWidth - margin.left - margin.right;
        const height = document.getElementById('chart').offsetHeight - margin.top - margin.bottom;
        
        const svg = d3.select("#chart")
            .append("svg")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom)
            .append("g")
            .attr("transform", `translate(${margin.left},${margin.top})`);
            
        // X axis
        const x = d3.scaleBand()
            .domain(chartData.map(d => d.year))
            .range([0, width])
            .padding(0.3);
            
        svg.append("g")
            .attr("transform", `translate(0,${height})`)
            .call(d3.axisBottom(x))
            .selectAll("text")
            .attr("fill", "white");
            
        // Y axis
        const y = d3.scaleLinear()
            .domain([0, 600])
            .range([height, 0]);
            
        svg.append("g")
            .call(d3.axisLeft(y))
            .selectAll("text")
            .attr("fill", "white");
            
        // Add title
        svg.append("text")
            .attr("x", width / 2)
            .attr("y", 0)
            .attr("text-anchor", "middle")
            .attr("fill", "white")
            .text("微短剧市场规模增长（单位：亿元）");
            
        // Add bars
        svg.selectAll("mybar")
            .data(chartData)
            .join("rect")
            .attr("x", d => x(d.year))
            .attr("y", d => y(d.value))
            .attr("width", x.bandwidth())
            .attr("height", d => height - y(d.value))
            .attr("fill", "#ff7b00");
            
        // Add value labels on top of bars
        svg.selectAll("text.bar")
            .data(chartData)
            .join("text")
            .attr("class", "bar")
            .attr("x", d => x(d.year) + x.bandwidth()/2)
            .attr("y", d => y(d.value) - 10)
            .attr("text-anchor", "middle")
            .attr("fill", "white")
            .text(d => d.value);
    </script>

</body></html>