#!/usr/bin/env python3
"""
批量HTML转PPTX工具
专门用于处理项目中的多个HTML文件
"""

import os
import glob
from webpage_to_pptx import WebPageToPPTX
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BatchHTMLToPPTX:
    """批量HTML转PPTX处理器"""
    
    def __init__(self):
        self.converter = WebPageToPPTX()
    
    def process_all_html_files(self, input_dir: str = ".", output_dir: str = "pptx_output"):
        """处理目录中的所有HTML文件"""
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 查找所有HTML文件
        html_files = glob.glob(os.path.join(input_dir, "*.html"))
        
        if not html_files:
            logger.warning(f"在目录 {input_dir} 中未找到HTML文件")
            return
        
        logger.info(f"找到 {len(html_files)} 个HTML文件")
        
        for html_file in html_files:
            try:
                # 生成输出文件名
                base_name = os.path.splitext(os.path.basename(html_file))[0]
                output_file = os.path.join(output_dir, f"{base_name}.pptx")
                
                logger.info(f"正在处理: {html_file}")
                
                # 创建新的转换器实例（避免幻灯片累积）
                converter = WebPageToPPTX()
                converter.convert_file_to_pptx(html_file, output_file)
                
                logger.info(f"完成: {output_file}")
                
            except Exception as e:
                logger.error(f"处理文件 {html_file} 时出错: {str(e)}")
                continue
    
    def merge_html_files_to_single_pptx(self, input_dir: str = ".", output_file: str = "merged_presentation.pptx"):
        """将多个HTML文件合并为一个PPTX"""
        html_files = sorted(glob.glob(os.path.join(input_dir, "*.html")))
        
        if not html_files:
            logger.warning(f"在目录 {input_dir} 中未找到HTML文件")
            return
        
        logger.info(f"将 {len(html_files)} 个HTML文件合并为一个PPTX")
        
        # 创建主标题幻灯片
        self.converter.create_title_slide("AI短剧生成应用演示")
        
        for i, html_file in enumerate(html_files, 1):
            try:
                logger.info(f"正在处理第 {i} 个文件: {html_file}")
                
                soup = self.converter.read_local_html(html_file)
                content = self.converter.extract_content(soup)
                
                # 为每个文件创建分节标题
                file_name = os.path.splitext(os.path.basename(html_file))[0]
                section_title = f"第 {i} 部分: {content.get('title', file_name)}"
                self.converter.create_content_slide(section_title, [])
                
                # 处理文件内容
                self.converter.process_content(content)
                
            except Exception as e:
                logger.error(f"处理文件 {html_file} 时出错: {str(e)}")
                continue
        
        # 保存合并的演示文稿
        self.converter.save_presentation(output_file)
        logger.info(f"合并完成: {output_file}")


def main():
    """主函数"""
    print("=== 批量HTML转PPTX工具 ===")
    print("1. 将每个HTML文件转换为单独的PPTX")
    print("2. 将所有HTML文件合并为一个PPTX")
    print("3. 退出")
    
    choice = input("请选择操作 (1-3): ").strip()
    
    batch_processor = BatchHTMLToPPTX()
    
    if choice == "1":
        print("正在将每个HTML文件转换为单独的PPTX...")
        batch_processor.process_all_html_files()
        print("批量转换完成！请查看 pptx_output 目录")
        
    elif choice == "2":
        output_name = input("请输入输出文件名 (默认: merged_presentation.pptx): ").strip()
        if not output_name:
            output_name = "merged_presentation.pptx"
        
        print("正在合并所有HTML文件为一个PPTX...")
        batch_processor.merge_html_files_to_single_pptx(output_file=output_name)
        print(f"合并完成！输出文件: {output_name}")
        
    elif choice == "3":
        print("退出程序")
        
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
