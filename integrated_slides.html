<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>AI短剧生成应用 - 完整演示</title>
    
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap');
        
        body {
            width: 1280px; 
            min-height: 720px; 
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .slide-container {
            width: 1280px;
            min-height: 720px;
            position: relative;
        }
        
        .slide {
            width: 1280px;
            min-height: 720px;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.5s ease-in-out;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }
        
        .slide.prev {
            transform: translateX(-100%);
        }
        
        /* Navigation */
        .nav-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 10px;
            background: rgba(0,0,0,0.7);
            padding: 10px 20px;
            border-radius: 25px;
        }
        
        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background: rgba(255,255,255,0.4);
        }
        
        .slide-indicator {
            display: flex;
            gap: 5px;
            align-items: center;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .dot.active {
            background: white;
            transform: scale(1.2);
        }
        
        /* Slide-specific styles */
        .gradient-bg { background: linear-gradient(135deg, #2d1b4e 0%, #7c2762 100%); }
        .blue-gradient { background: linear-gradient(135deg, #0f2a4a 0%, #1a4a7c 100%); }
        .dark-blue-gradient { background: linear-gradient(135deg, #1a365d 0%, #2a4365 100%); }
        
        .title { font-size: 2.5rem; font-weight: bold; margin-bottom: 1.5rem; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5); }
        .content-box { background-color: rgba(255, 255, 255, 0.1); backdrop-filter: blur(5px); border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .feature-pill { background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; margin: 4px; display: inline-flex; align-items: center; }
        .highlight-text { background: linear-gradient(90deg, #ff9966, #ff5e62); -webkit-background-clip: text; background-clip: text; color: transparent; display: inline; }
        
        /* Keyboard navigation hint */
        .keyboard-hint {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="keyboard-hint">
        使用 ← → 键或点击下方按钮导航
    </div>

    <div class="slide-container">
        <!-- Slide 1: 封面 -->
        <div class="slide active gradient-bg flex flex-col justify-between p-16 relative">
            <div class="absolute top-0 left-0 w-full h-20 bg-white opacity-5 transform -skew-y-6"></div>
            <div class="absolute bottom-40 left-0 w-1/3 h-20 bg-white opacity-5 transform skew-y-6"></div>
            
            <div class="z-10 relative">
                <div class="mb-2 text-gray-300 text-xl font-medium tracking-wider">全流程 · 高效率 · 人机协同</div>
                <h1 class="text-6xl font-black text-white title-text mb-4">未来内容创作<span class="highlight-text">新纪元</span></h1>
                <h2 class="text-5xl font-bold text-white title-text mb-12">AI短剧生成应用</h2>
                <div class="mt-8 max-w-2xl">
                    <p class="text-gray-200 text-xl mb-8">从创意到成片，一键式智能短剧制作平台，<br/>让每个人都能成为内容创作者</p>
                    <div class="flex flex-wrap mt-6">
                        <div class="feature-pill text-white"><i class="fas fa-pen-fancy mr-2"></i><span>智能剧本创作</span></div>
                        <div class="feature-pill text-white"><i class="fas fa-user-circle mr-2"></i><span>人物形象塑造</span></div>
                        <div class="feature-pill text-white"><i class="fas fa-film mr-2"></i><span>视频内容制作</span></div>
                        <div class="feature-pill text-white"><i class="fas fa-microphone mr-2"></i><span>音频处理与同步</span></div>
                        <div class="feature-pill text-white"><i class="fas fa-cut mr-2"></i><span>视频后期处理</span></div>
                    </div>
                </div>
            </div>
            
            <div class="z-10 text-gray-300 text-sm">
                <p>2025-08-04</p>
            </div>
        </div>

        <!-- Slide 2: 市场背景 -->
        <div class="slide blue-gradient p-12 flex flex-col">
            <h1 class="title text-center mb-8">市场背景与机遇</h1>
            <div class="flex flex-1 space-x-6">
                <div class="w-1/2">
                    <div class="content-box p-6 h-full">
                        <h2 class="text-2xl font-semibold mb-4">短视频市场爆发式增长</h2>
                        <div id="market-chart" class="mb-6"></div>
                        <p class="text-gray-200">短视频内容消费需求持续攀升，市场规模预计2025年将突破500亿元</p>
                    </div>
                </div>
                <div class="w-1/2">
                    <div class="flex flex-col space-y-6">
                        <div class="bg-red-900 bg-opacity-30 p-4 rounded-lg">
                            <h3 class="text-xl font-medium mb-2 flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-400 mr-2"></i>传统制作模式的痛点
                            </h3>
                            <ul class="list-disc list-inside space-y-2 text-gray-200">
                                <li>制作周期长，成本高昂</li>
                                <li>流程繁琐，效率低下</li>
                                <li>难以满足内容快速迭代需求</li>
                                <li>创意表达受限，缺乏多样性</li>
                            </ul>
                        </div>
                        <div class="bg-green-900 bg-opacity-30 p-4 rounded-lg">
                            <h3 class="text-xl font-medium mb-2 flex items-center">
                                <i class="fas fa-check-circle text-green-400 mr-2"></i>AI短剧生成应用的创新解决方案
                            </h3>
                            <ul class="list-disc list-inside space-y-2 text-gray-200">
                                <li>AI赋能+人工主导的协同创作模式</li>
                                <li>将灵感到成片的完整创作链路集于一体</li>
                                <li>实现制作效率的指数级提升</li>
                                <li>大幅压缩创作成本</li>
                                <li>激发无限创意可能</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: 智能剧本创作 -->
        <div class="slide dark-blue-gradient p-12 flex flex-col">
            <div class="mb-8">
                <h1 class="title">智能剧本创作</h1>
                <p class="text-xl mt-4 text-gray-200">AI驱动的创意写作新时代</p>
            </div>
            <div class="flex flex-1">
                <div class="w-1/2 pr-8 flex flex-col justify-between">
                    <div class="content-box p-4 mb-4">
                        <div class="flex items-start">
                            <i class="fas fa-magic text-orange-400 text-2xl mr-4 mt-1"></i>
                            <div>
                                <h3 class="text-xl font-semibold mb-2">一键启动创意</h3>
                                <p class="text-gray-300">只需输入主题、风格或梗概，AI大语言模型迅速构建完整故事世界，将创意转化为结构化剧本</p>
                            </div>
                        </div>
                    </div>
                    <div class="content-box p-4 mb-4">
                        <div class="flex items-start">
                            <i class="fas fa-users text-orange-400 text-2xl mr-4 mt-1"></i>
                            <div>
                                <h3 class="text-xl font-semibold mb-2">角色深度塑造</h3>
                                <p class="text-gray-300">智能分析角色性格特征，生成个性化对话风格，确保每个角色都有独特的声音和表达方式</p>
                            </div>
                        </div>
                    </div>
                    <div class="content-box p-4">
                        <div class="flex items-start">
                            <i class="fas fa-chart-line text-orange-400 text-2xl mr-4 mt-1"></i>
                            <div>
                                <h3 class="text-xl font-semibold mb-2">情节智能优化</h3>
                                <p class="text-gray-300">基于叙事理论和观众心理学，自动调整剧情节奏，优化冲突设置和情感起伏</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-1/2 pl-8 flex flex-col">
                    <div class="mb-6 rounded-lg overflow-hidden">
                        <img alt="AI剧本生成界面" class="w-full h-auto object-cover shadow-lg" src="https://via.placeholder.com/400x300/4A90E2/FFFFFF?text=AI+Script+Generation" style="max-height: 350px;"/>
                    </div>
                    <div class="content-box p-6 mt-auto">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-robot text-orange-400 text-2xl mr-4"></i>
                            <i class="fas fa-user-edit text-orange-400 text-2xl mr-4"></i>
                            <h3 class="text-xl font-semibold">人机协同创作模式</h3>
                        </div>
                        <p class="text-gray-300">AI提供创意基础和结构框架，创作者专注于情感表达和艺术加工，实现效率与创意的完美平衡</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: 人物形象塑造 -->
        <div class="slide blue-gradient p-12 flex flex-col">
            <h1 class="title text-center mb-8">人物形象塑造</h1>
            <div class="flex flex-1 space-x-6">
                <div class="w-1/2">
                    <div class="content-box p-6 h-full">
                        <h2 class="text-2xl font-semibold mb-4 flex items-center">
                            <i class="fas fa-palette text-orange-400 mr-3"></i>视觉形象设计
                        </h2>
                        <div class="space-y-4">
                            <div class="bg-blue-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">多样化风格选择</h3>
                                <p class="text-gray-300 text-sm">支持写实、动漫、卡通等多种艺术风格，满足不同类型短剧的视觉需求</p>
                            </div>
                            <div class="bg-blue-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">精细化定制</h3>
                                <p class="text-gray-300 text-sm">从五官特征到服装搭配，从表情神态到肢体语言，全方位塑造独特角色形象</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-1/2">
                    <div class="content-box p-6 h-full">
                        <h2 class="text-2xl font-semibold mb-4 flex items-center">
                            <i class="fas fa-brain text-orange-400 mr-3"></i>性格特征构建
                        </h2>
                        <div class="space-y-4">
                            <div class="bg-purple-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">心理画像分析</h3>
                                <p class="text-gray-300 text-sm">基于角色背景和剧情需要，构建完整的性格特征和行为模式</p>
                            </div>
                            <div class="bg-purple-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">一致性保证</h3>
                                <p class="text-gray-300 text-sm">确保角色在不同场景下的表现保持逻辑一致性和可信度</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: 视频内容制作 -->
        <div class="slide blue-gradient p-12 flex flex-col">
            <h1 class="title text-center mb-8">视频内容制作</h1>
            <div class="flex flex-1 space-x-6">
                <div class="w-1/2">
                    <div class="content-box p-6 h-full">
                        <h2 class="text-2xl font-semibold mb-4 flex items-center">
                            <i class="fas fa-film text-orange-400 mr-2"></i>制作流程
                        </h2>
                        <div class="flex flex-col space-y-6 mt-8">
                            <div class="flex items-center p-4">
                                <div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                                    <i class="fas fa-pencil-ruler text-2xl text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-medium">分镜设计</h3>
                                    <p class="text-gray-300">将剧本转化为可预览的画面，规划每个镜头的构图、景别、角色动态和镜头运动</p>
                                </div>
                            </div>
                            <div class="flex items-center p-4">
                                <div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                                    <i class="fas fa-image text-2xl text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-medium">文生图技术</h3>
                                    <p class="text-gray-300">将场景描述、人物造型、光影效果的文字描述，转化为高质量静态图像</p>
                                </div>
                            </div>
                            <div class="flex items-center p-4">
                                <div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                                    <i class="fas fa-video text-2xl text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-medium">图生视频</h3>
                                    <p class="text-gray-300">基于静态图像生成流畅的视频片段，实现角色动作和场景变化</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-1/2">
                    <div class="content-box p-6 h-full">
                        <h2 class="text-2xl font-semibold mb-4 flex items-center">
                            <i class="fas fa-robot text-orange-400 mr-2"></i>AI模型对比
                        </h2>
                        <div class="space-y-4">
                            <div class="bg-blue-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">海螺 (Hailuo) AI</h3>
                                <p class="text-gray-300 text-sm">强调高质量视觉体验，色彩表达和情感表达出色，支持镜头控制和主体控制</p>
                            </div>
                            <div class="bg-blue-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">Runway Gen-3</h3>
                                <p class="text-gray-300 text-sm">专业级视频生成，运动控制精准，适合复杂场景和动作序列</p>
                            </div>
                            <div class="bg-blue-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">Luma Dream Machine</h3>
                                <p class="text-gray-300 text-sm">快速生成，适合原型制作和创意验证，成本效益高</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: 音频处理与同步 -->
        <div class="slide blue-gradient p-12 flex flex-col">
            <h1 class="title text-center mb-8">音频处理与同步</h1>
            <div class="flex flex-1 space-x-6">
                <div class="w-1/2">
                    <div class="content-box p-6 h-full">
                        <h2 class="text-2xl font-semibold mb-4 flex items-center">
                            <i class="fas fa-microphone-alt text-orange-400 mr-3"></i>一键配音生成
                        </h2>
                        <div class="space-y-4">
                            <div class="bg-blue-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">智能情感分析</h3>
                                <p class="text-gray-300 text-sm">AI深度理解剧本中的角色情绪和场景氛围，自动匹配最合适的语气、语速和情感色彩</p>
                            </div>
                            <div class="bg-blue-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">多音色选择</h3>
                                <p class="text-gray-300 text-sm">提供丰富的音色库，支持不同年龄、性别、性格特征的声音定制</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-1/2">
                    <div class="content-box p-6 h-full">
                        <h2 class="text-2xl font-semibold mb-4 flex items-center">
                            <i class="fas fa-sync-alt text-orange-400 mr-3"></i>精准对口型
                        </h2>
                        <div class="space-y-4">
                            <div class="bg-purple-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">音频特征提取</h3>
                                <p class="text-gray-300 text-sm">分析语音的音素、音调和节奏特征</p>
                            </div>
                            <div class="bg-purple-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">唇形动画生成</h3>
                                <p class="text-gray-300 text-sm">根据音频特征生成精确的唇形动画</p>
                            </div>
                            <div class="bg-purple-800 bg-opacity-30 p-4 rounded-lg">
                                <h3 class="text-lg font-medium mb-2">实时同步渲染</h3>
                                <p class="text-gray-300 text-sm">确保音频与视觉的完美同步</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: 视频后期处理 -->
        <div class="slide blue-gradient p-12 flex flex-col">
            <h1 class="title text-center mb-8">视频后期处理</h1>
            <div class="text-center mb-6">
                <p class="text-xl">内置简易剪辑功能，无需专业软件即可完成从创意到成品的<span class="text-orange-400 font-bold">"最后一公里"</span></p>
            </div>
            <div class="grid grid-cols-2 gap-6 flex-1">
                <div class="content-box p-5 flex">
                    <div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                        <i class="fas fa-cut text-3xl text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2">基础剪辑操作</h3>
                        <p class="text-gray-300">提供直观的拖拽式时间线，轻松完成视频片段的裁剪、分割、合并和排序</p>
                    </div>
                </div>
                <div class="content-box p-5 flex">
                    <div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                        <i class="fas fa-magic text-3xl text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2">丰富的转场效果</h3>
                        <p class="text-gray-300">内置多种预设转场动画，轻松实现平滑自然的场景过渡</p>
                    </div>
                </div>
                <div class="content-box p-5 flex">
                    <div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                        <i class="fas fa-microphone-alt text-3xl text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2">音频与字幕添加</h3>
                        <p class="text-gray-300">便捷添加背景音乐、音效，并支持快速添加、编辑对话字幕</p>
                    </div>
                </div>
                <div class="content-box p-5 flex">
                    <div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                        <i class="fas fa-filter text-3xl text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold mb-2">滤镜与特效</h3>
                        <p class="text-gray-300">丰富的滤镜库和特效选项，提升视频的视觉表现力</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 8: 核心竞争优势 -->
        <div class="slide blue-gradient p-12 flex flex-col">
            <h1 class="title text-center mb-8">核心竞争优势</h1>
            <div class="flex-1 grid grid-cols-3 gap-6">
                <div class="content-box p-6 flex flex-col text-center">
                    <div class="mb-4">
                        <i class="fas fa-cube text-4xl text-orange-400"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">一站式解决方案</h3>
                    <p class="text-gray-300">将从灵感到成片的完整创作链路集于一体，实现全流程一体化服务</p>
                </div>
                <div class="content-box p-6 flex flex-col text-center">
                    <div class="mb-4">
                        <i class="fas fa-bolt text-4xl text-orange-400"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">效率大幅提升</h3>
                    <p class="text-gray-300">相比传统制作模式，制作效率提升10倍以上，大幅缩短制作周期</p>
                </div>
                <div class="content-box p-6 flex flex-col text-center">
                    <div class="mb-4">
                        <i class="fas fa-dollar-sign text-4xl text-orange-400"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">成本显著降低</h3>
                    <p class="text-gray-300">无需专业团队和昂贵设备，制作成本降低90%以上</p>
                </div>
                <div class="content-box p-6 flex flex-col text-center">
                    <div class="mb-4">
                        <i class="fas fa-users text-4xl text-orange-400"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">降低创作门槛</h3>
                    <p class="text-gray-300">让每个人都能成为内容创作者，无需专业技能即可制作高质量短剧</p>
                </div>
                <div class="content-box p-6 flex flex-col text-center">
                    <div class="mb-4">
                        <i class="fas fa-lightbulb text-4xl text-orange-400"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">创意无限可能</h3>
                    <p class="text-gray-300">AI技术突破传统创作限制，实现任何想象都能成为现实</p>
                </div>
                <div class="content-box p-6 flex flex-col text-center">
                    <div class="mb-4">
                        <i class="fas fa-rocket text-4xl text-orange-400"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">快速迭代优化</h3>
                    <p class="text-gray-300">支持快速修改和优化，满足内容快速迭代的市场需求</p>
                </div>
            </div>
        </div>

        <!-- Slide 9: 联系我们 -->
        <div class="slide blue-gradient p-12 flex flex-col items-center justify-center relative">
            <h1 class="title text-center mb-6">联系我们</h1>
            <p class="text-xl text-center mb-8 text-gray-200">开启您的AI短剧创作之旅</p>
            
            <div class="content-box p-8 max-w-2xl w-full text-center">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="flex items-center justify-center">
                        <i class="fas fa-envelope text-2xl text-orange-400 mr-3"></i>
                        <span class="text-lg"><EMAIL></span>
                    </div>
                    <div class="flex items-center justify-center">
                        <i class="fas fa-phone text-2xl text-orange-400 mr-3"></i>
                        <span class="text-lg">************</span>
                    </div>
                    <div class="flex items-center justify-center">
                        <i class="fas fa-map-marker-alt text-2xl text-orange-400 mr-3"></i>
                        <span class="text-lg">北京市朝阳区</span>
                    </div>
                    <div class="flex items-center justify-center">
                        <i class="fas fa-globe text-2xl text-orange-400 mr-3"></i>
                        <span class="text-lg">www.aishortdrama.com</span>
                    </div>
                </div>
                
                <div class="border-t border-gray-600 pt-6">
                    <p class="text-gray-300 mb-4">扫码关注我们，获取最新产品动态</p>
                    <div class="flex justify-center space-x-6">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-2">
                                <i class="fab fa-weixin text-2xl"></i>
                            </div>
                            <p class="text-sm">微信公众号</p>
                        </div>
                        <div class="text-center">
                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-2">
                                <i class="fab fa-weibo text-2xl"></i>
                            </div>
                            <p class="text-sm">官方微博</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <p class="text-center text-gray-400 mt-8">感谢您的关注！让我们一起创造内容创作的美好未来</p>
        </div>
    </div>

    <!-- Navigation Controls -->
    <div class="nav-controls">
        <button class="nav-btn" onclick="prevSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="slide-indicator">
            <div class="dot active" onclick="goToSlide(0)"></div>
            <div class="dot" onclick="goToSlide(1)"></div>
            <div class="dot" onclick="goToSlide(2)"></div>
            <div class="dot" onclick="goToSlide(3)"></div>
            <div class="dot" onclick="goToSlide(4)"></div>
            <div class="dot" onclick="goToSlide(5)"></div>
            <div class="dot" onclick="goToSlide(6)"></div>
            <div class="dot" onclick="goToSlide(7)"></div>
            <div class="dot" onclick="goToSlide(8)"></div>
        </div>
        
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.dot');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            dots[currentSlide].classList.remove('active');
            
            currentSlide = (n + totalSlides) % totalSlides;
            
            slides[currentSlide].classList.add('active');
            dots[currentSlide].classList.add('active');
            
            // Initialize chart on slide 2
            if (currentSlide === 1) {
                setTimeout(initChart, 500);
            }
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function prevSlide() {
            showSlide(currentSlide - 1);
        }

        function goToSlide(n) {
            showSlide(n);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight') nextSlide();
            if (e.key === 'ArrowLeft') prevSlide();
        });

        // Initialize market chart for slide 2
        function initChart() {
            const chartContainer = document.getElementById('market-chart');
            if (!chartContainer || chartContainer.innerHTML.trim() !== '') return;
            
            const data = [
                {year: "2023", value: 300},
                {year: "2024", value: 400},
                {year: "2025", value: 505}
            ];
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = 300 - margin.left - margin.right;
            const height = 200 - margin.bottom - margin.top;
            
            const svg = d3.select('#market-chart')
                .append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
                
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
                
            const x = d3.scaleBand()
                .domain(data.map(d => d.year))
                .range([0, width])
                .padding(0.1);
                
            const y = d3.scaleLinear()
                .domain([0, d3.max(data, d => d.value)])
                .range([height, 0]);
                
            g.selectAll('.bar')
                .data(data)
                .enter().append('rect')
                .attr('class', 'bar')
                .attr('x', d => x(d.year))
                .attr('width', x.bandwidth())
                .attr('y', height)
                .attr('height', 0)
                .attr('fill', '#ff9966')
                .transition()
                .duration(1000)
                .attr('y', d => y(d.value))
                .attr('height', d => height - y(d.value));
                
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(x))
                .selectAll('text')
                .style('fill', 'white');
                
            g.append('g')
                .call(d3.axisLeft(y))
                .selectAll('text')
                .style('fill', 'white');
                
            g.selectAll('.bar')
                .data(data)
                .append('text')
                .attr('x', d => x(d.year) + x.bandwidth()/2)
                .attr('y', d => y(d.value) - 5)
                .attr('text-anchor', 'middle')
                .style('fill', 'white')
                .text(d => d.value + '亿');
        }

        // Auto-advance slides (optional)
        // setInterval(nextSlide, 10000);
    </script>
</body>
</html>