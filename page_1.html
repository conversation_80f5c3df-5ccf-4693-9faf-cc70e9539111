<!DOCTYPE html><html><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap');
        body {width: 1280px; min-height: 720px; font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;}
        .slide-container {
            width: 1280px;
            min-height: 720px;
            position: relative;
            
        }
        .gradient-bg {
            background: linear-gradient(135deg, #2d1b4e 0%, #7c2762 100%);
        }
        .title-text {
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        }
        .highlight-text {
            background: linear-gradient(90deg, #ff9966, #ff5e62);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            display: inline;
        }
        .image-overlay {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 650px;
            height: 500px;
            
            border-radius: 20px 0 0 0;
            opacity: 0.9;
        }
        .image-overlay img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        .feature-pill {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            border-radius: 20px;
            padding: 8px 16px;
            margin-right: 10px;
            margin-bottom: 10px;
            display: inline-flex;
            align-items: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
<div class="slide-container gradient-bg flex flex-col justify-between p-16 relative">
<!-- Decorative elements -->
<div class="absolute top-0 left-0 w-full h-20 bg-white opacity-5 transform -skew-y-6"></div>
<div class="absolute bottom-40 left-0 w-1/3 h-20 bg-white opacity-5 transform skew-y-6"></div>
<!-- Main content -->
<div class="z-10 relative">
<div class="mb-2 text-gray-300 text-xl font-medium tracking-wider">全流程 · 高效率 · 人机协同</div>
<h1 class="text-6xl font-black text-white title-text mb-4">未来内容创作<span class="highlight-text">新纪元</span></h1>
<h2 class="text-5xl font-bold text-white title-text mb-12">AI短剧生成应用</h2>
<div class="mt-8 max-w-2xl">
<p class="text-gray-200 text-xl mb-8">从创意到成片，一键式智能短剧制作平台，<br/>让每个人都能成为内容创作者</p>
<div class="flex flex-wrap mt-6">
<div class="feature-pill text-white">
<i class="fas fa-pen-fancy mr-2"></i>
<span>智能剧本创作</span>
</div>
<div class="feature-pill text-white">
<i class="fas fa-user-circle mr-2"></i>
<span>人物形象塑造</span>
</div>
<div class="feature-pill text-white">
<i class="fas fa-film mr-2"></i>
<span>视频内容制作</span>
</div>
<div class="feature-pill text-white">
<i class="fas fa-microphone mr-2"></i>
<span>音频处理与同步</span>
</div>
<div class="feature-pill text-white">
<i class="fas fa-cut mr-2"></i>
<span>视频后期处理</span>
</div>
</div>
</div>
</div>
<!-- Bottom info -->
<div class="z-10 text-gray-300 text-sm">
<p>2025-08-04</p>
</div>
<!-- Image overlay -->
<div class="image-overlay">
<img alt="AI创意交互场景" src="https://picture-search.skywork.ai/aippt/image/sheet/a7c59d0dc210f43cd5f3975e0f971ba8_crop.jpg" data-sk-source-container="img" data-sk-source-url="https://www.dapingtime.com/article/542.html" data-sk-source-id="1952183924398309380"/>
<div class="absolute inset-0 bg-gradient-to-t from-transparent to-purple-900 opacity-30"></div>
</div>
</div>

</body></html>