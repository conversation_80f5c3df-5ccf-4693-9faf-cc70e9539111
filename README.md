# 网页转PPTX工具

这是一个Python工具，可以将网页内容转换为PowerPoint演示文稿（PPTX格式）。

## 功能特点

- 支持在线网页URL转换
- 支持本地HTML文件转换
- 批量处理多个HTML文件
- 自动提取网页标题、段落、列表等内容
- 生成格式化的PowerPoint幻灯片
- 支持合并多个HTML文件为一个演示文稿

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 单个网页/文件转换

#### 转换在线网页
```bash
python webpage_to_pptx.py --url https://example.com --output my_presentation.pptx
```

#### 转换本地HTML文件
```bash
python webpage_to_pptx.py --file page_1.html --output page_1.pptx
```

### 2. 批量处理

运行批量处理脚本：
```bash
python batch_html_to_pptx.py
```

然后选择操作：
- 选项1：将每个HTML文件转换为单独的PPTX文件
- 选项2：将所有HTML文件合并为一个PPTX文件

### 3. 在Python代码中使用

```python
from webpage_to_pptx import WebPageToPPTX

# 创建转换器
converter = WebPageToPPTX()

# 转换网页
converter.convert_url_to_pptx('https://example.com', 'output.pptx')

# 转换本地文件
converter.convert_file_to_pptx('page_1.html', 'page_1.pptx')
```

## 输出说明

- 每个PPTX文件包含：
  - 标题幻灯片
  - 内容幻灯片（根据网页内容自动分页）
  - 列表幻灯片（如果网页包含列表）

- 批量处理输出：
  - 单独转换：在 `pptx_output/` 目录下生成多个PPTX文件
  - 合并转换：生成一个包含所有内容的PPTX文件

## 支持的内容类型

- 网页标题
- 各级标题 (H1-H6)
- 段落文本
- 有序和无序列表
- 基本文本格式

## 注意事项

1. 确保网络连接正常（转换在线网页时）
2. HTML文件编码建议使用UTF-8
3. 生成的PPTX文件可以用Microsoft PowerPoint或其他兼容软件打开
4. 长文本会自动分页以保证可读性

## 扩展功能

如需添加网页截图功能，可以安装额外依赖：
```bash
pip install selenium Pillow webdriver-manager
```

## 故障排除

- 如果遇到编码问题，请确保HTML文件使用UTF-8编码
- 如果网页加载失败，请检查网络连接和URL有效性
- 如果生成的PPTX内容不完整，可能是网页结构复杂，建议检查HTML源码
