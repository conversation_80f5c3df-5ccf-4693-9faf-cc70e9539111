<!DOCTYPE html><html lang="zh"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>视频后期处理</title>

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        body {width: 1280px; min-height: 720px; margin: 0;
            padding: 0;}
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background: linear-gradient(135deg, #0f2a4a 0%, #1a4a7c 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .content-box {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card {
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
        }
        .accent-text {
            color: #ff7b00;
        }
        .timeline-container {
            position: relative;
            height: 80px;
        }
        .timeline {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%);
        }
        .timeline-node {
            position: absolute;
            top: 50%;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #ff7b00;
            transform: translate(-50%, -50%);
        }
        .timeline-label {
            position: absolute;
            top: 100%;
            transform: translateX(-50%);
            text-align: center;
            width: 100px;
            margin-top: 8px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
<div class="slide p-12 flex flex-col">
<!-- Title -->
<h1 class="title text-center mb-8">视频后期处理</h1>
<!-- Main Content -->
<div class="flex-1 flex flex-col">
<!-- Introduction -->
<div class="text-center mb-6">
<p class="text-xl">
                    内置简易剪辑功能，无需专业软件即可完成从创意到成品的<span class="accent-text font-bold">&#34;最后一公里&#34;</span>
</p>
</div>
<!-- Timeline -->
<div class="timeline-container mb-8">
<div class="timeline"></div>
<div class="timeline-node" style="left: 10%;">
<div class="timeline-label">AI生成内容</div>
</div>
<div class="timeline-node" style="left: 30%;">
<div class="timeline-label">基础剪辑</div>
</div>
<div class="timeline-node" style="left: 50%;">
<div class="timeline-label">特效添加</div>
</div>
<div class="timeline-node" style="left: 70%;">
<div class="timeline-label">音频优化</div>
</div>
<div class="timeline-node" style="left: 90%;">
<div class="timeline-label">成品导出</div>
</div>
</div>
<!-- Features Grid -->
<div class="grid grid-cols-2 gap-6 flex-1">
<!-- Basic Editing -->
<div class="feature-card p-5 flex">
<div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4 flex-shrink-0">
<i class="fas fa-cut text-3xl text-white"></i>
</div>
<div>
<h3 class="text-xl font-semibold mb-2">基础剪辑操作</h3>
<p class="text-gray-300">提供直观的拖拽式时间线，轻松完成视频片段的裁剪、分割、合并和排序，优化叙事节奏</p>
</div>
</div>
<!-- Transition Effects -->
<div class="feature-card p-5 flex">
<div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4 flex-shrink-0">
<i class="fas fa-magic text-3xl text-white"></i>
</div>
<div>
<h3 class="text-xl font-semibold mb-2">丰富的转场效果</h3>
<p class="text-gray-300">内置多种预设转场动画（如淡入淡出、叠化、擦除等），轻松实现平滑自然的场景过渡</p>
</div>
</div>
<!-- Audio and Subtitles -->
<div class="feature-card p-5 flex">
<div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4 flex-shrink-0">
<i class="fas fa-microphone-alt text-3xl text-white"></i>
</div>
<div>
<h3 class="text-xl font-semibold mb-2">音频与字幕添加</h3>
<p class="text-gray-300">便捷添加背景音乐(BGM)、音效，并支持调节音量大小；强大的字幕工具可快速添加、编辑对话字幕或旁白</p>
</div>
</div>
<!-- Filters -->
<div class="feature-card p-5 flex">
<div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4 flex-shrink-0">
<i class="fas fa-palette text-3xl text-white"></i>
</div>
<div>
<h3 class="text-xl font-semibold mb-2">滤镜与画面调优</h3>
<p class="text-gray-300">提供多种风格化滤镜，一键改变视频整体色调和氛围；支持对亮度、对比度、饱和度等参数进行微调</p>
</div>
</div>
</div>
<!-- Bottom Highlight -->
<div class="mt-6 text-center">
<div class="inline-block bg-blue-800 bg-opacity-50 rounded-full px-6 py-2 text-lg">
<i class="fas fa-check-circle text-green-400 mr-2"></i>
                    一体化剪辑工具，让每一位用户都能轻松为作品增添专业质感
                </div>
</div>
</div>
</div>

</body></html>