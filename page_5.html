<!DOCTYPE html><html lang="zh"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>视频内容制作</title>

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com"></script><style>
        body {width: 1280px; min-height: 912px; margin: 0;
            padding: 0;}
        .slide {
            width: 1280px;
            min-height: 912px;
            position: relative;
            background: linear-gradient(135deg, #0f2a4a 0%, #1a4a7c 100%);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .content-box {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .process-step {
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
        }
        .process-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
        }
        .process-arrow {
            position: absolute;
            top: 50%;
            right: -30px;
            transform: translateY(-50%);
            color: #ff7b00;
            font-size: 24px;
            z-index: 10;
        }
        .model-card {
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
        }
        .accent-text {
            color: #ff7b00;
        }
        .highlight-box {
            border-left: 4px solid #ff7b00;
        }
    </style>
</head>
<body>
<div class="slide p-12 flex flex-col">
<!-- Title -->
<h1 class="title text-center mb-8">视频内容制作</h1>
<!-- Main Content -->
<div class="flex flex-1 space-x-6">
<!-- Left Column - Production Process -->
<div class="w-1/2">
<div class="content-box p-6 h-full">
<h2 class="text-2xl font-semibold mb-4 flex items-center">
<i class="fas fa-film text-orange-400 mr-2"></i>制作流程
                    </h2>
<!-- Process Steps -->
<div class="flex flex-col space-y-6 mt-8">
<!-- Step 1: Storyboard -->
<div class="process-step p-4 flex items-center">
<div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4 flex-shrink-0">
<i class="fas fa-pencil-ruler text-2xl text-white"></i>
</div>
<div>
<h3 class="text-xl font-medium">分镜设计</h3>
<p class="text-gray-300">将剧本转化为可预览的画面，规划每个镜头的构图、景别、角色动态和镜头运动</p>
</div>
<div class="process-arrow">
<i class="fas fa-chevron-down"></i>
</div>
</div>
<!-- Step 2: Text-to-Image -->
<div class="process-step p-4 flex items-center">
<div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4 flex-shrink-0">
<i class="fas fa-image text-2xl text-white"></i>
</div>
<div>
<h3 class="text-xl font-medium">文生图技术</h3>
<p class="text-gray-300">将场景描述、人物造型、光影效果的文字描述，转化为高质量静态图像</p>
</div>
<div class="process-arrow">
<i class="fas fa-chevron-down"></i>
</div>
</div>
<!-- Step 3: Image-to-Video -->
<div class="process-step p-4 flex items-center">
<div class="w-16 h-16 rounded-full bg-blue-700 flex items-center justify-center mr-4 flex-shrink-0">
<i class="fas fa-video text-2xl text-white"></i>
</div>
<div>
<h3 class="text-xl font-medium">图生视频技术</h3>
<p class="text-gray-300">将静态画面转化为动态视频，模拟人物表情变化、光影流动和物体自然运动</p>
</div>
</div>
</div>
<!-- Highlight Box -->
<div class="highlight-box bg-blue-900 bg-opacity-40 p-4 mt-8 rounded-r-lg">
<h3 class="text-lg font-medium mb-2">技术优势</h3>
<ul class="list-disc list-inside space-y-1 text-gray-200">
<li>保持视觉连贯性，解决&#34;一致性断裂&#34;问题</li>
<li>支持多种镜头运动，如推、拉、摇、移等</li>
<li>将静态画面转化为流畅、自然的动态效果</li>
</ul>
</div>
</div>
</div>
<!-- Right Column - AI Models Comparison -->
<div class="w-1/2">
<div class="content-box p-6 h-full">
<h2 class="text-2xl font-semibold mb-4 flex items-center">
<i class="fas fa-robot text-orange-400 mr-2"></i>多AI模型支持
                    </h2>
<!-- AI Models Comparison -->
<div class="flex flex-col space-y-6 mt-4">
<!-- Model 1: Hailuo AI -->
<div class="model-card p-5">
<div class="flex items-center mb-3">
<div class="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center mr-3">
<i class="fas fa-water text-xl text-white"></i>
</div>
<h3 class="text-xl font-medium">海螺 (Hailuo) AI</h3>
</div>
<div class="grid grid-cols-2 gap-3">
<div>
<p class="text-sm text-gray-300">技术优势</p>
<p class="text-sm">强调高质量视觉体验，色彩表达和情感表达出色</p>
</div>
<div>
<p class="text-sm text-gray-300">核心功能</p>
<p class="text-sm">支持镜头控制和主体控制，多模态交互创作</p>
</div>
<div class="col-span-2">
<p class="text-sm text-gray-300">适用场景</p>
<p class="text-sm">追求细腻艺术风格、丰富情感表达和电影级运镜效果的创作者</p>
</div>
</div>
</div>
<!-- Model 2: Keling AI -->
<div class="model-card p-5">
<div class="flex items-center mb-3">
<div class="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center mr-3">
<i class="fas fa-snowflake text-xl text-white"></i>
</div>
<h3 class="text-xl font-medium">可灵 (Keling) AI</h3>
</div>
<div class="grid grid-cols-2 gap-3">
<div>
<p class="text-sm text-gray-300">技术优势</p>
<p class="text-sm">采用类Sora架构，擅长处理大幅度、复杂的物理运动</p>
</div>
<div>
<p class="text-sm text-gray-300">核心功能</p>
<p class="text-sm">支持生成长达2分钟的1080p高清视频，提供运动笔刷</p>
</div>
<div class="col-span-2">
<p class="text-sm text-gray-300">适用场景</p>
<p class="text-sm">制作具有强烈动态感、复杂场景和物理交互的专业级短剧内容</p>
</div>
</div>
</div>
</div>
<!-- Bottom Note -->
<div class="bg-blue-900 bg-opacity-30 p-4 rounded-lg mt-6">
<p class="flex items-center">
<i class="fas fa-lightbulb text-yellow-400 mr-2"></i>
<span>通过整合多个AI引擎，我们的应用赋予用户前所未有的创作自由度，无论是宏大的科幻场景还是细腻的情感特写，都能找到最理想的视觉实现方案。</span>
</p>
</div>
</div>
</div>
</div>
<!-- Footer -->
<div class="mt-8 text-center text-sm text-gray-400">
<p>从文字到影像的飞跃，我们的应用将创意蓝图无缝转化为生动的视频画面</p>
</div>
</div>

</body></html>